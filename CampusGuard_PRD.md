# CampusGuard - 智能网络安全监控平台 PRD

## 1. 产品概述

### 1.1 产品名称
**CampusGuard** - 基于AI的网络安全监控平台

### 1.2 产品定位
CampusGuard是一个基于AI技术的简化版网络安全监控平台，专注于校园网络的基础监控、威胁检测和智能分析。采用简化架构设计，降低部署和维护复杂度，为中小型校园网络提供实用的安全监控解决方案。

### 1.3 产品目标
- **主要目标**：为校园网络提供基础而实用的安全监控和威胁检测能力
- **业务价值**：提升网络安全防护效率，减少人工监控工作量，提供基本的AI辅助分析功能
- **技术目标**：构建基于OpenAI Agents + DeepSeek V3的智能分析系统，支持中文自然语言交互

#### 1.3.1 基本功能要求
**威胁检测能力**：
- 能够识别常见的网络威胁和异常行为
- 提供基本的告警功能，减少误报
- 支持威胁情报查询和分析

**自然语言交互**：
- 支持中文自然语言查询设备状态和告警信息
- 提供基本的AI对话功能
- 支持常用的运维查询和操作

**监控覆盖**：
- 支持主流网络设备的SNMP监控
- 提供基本的网络拓扑显示
- 支持设备状态和性能监控

#### 1.3.2 性能目标（宽松标准）
**数据采集**：
- 设备状态监控：每10分钟更新一次
- 接口流量监控：每5分钟更新一次
- 支持50-100台设备的基本监控

**响应时间**：
- 页面加载：在合理时间内完成（通常5-10秒）
- 告警生成：在检测到异常后及时通知（通常5-15分钟内）
- AI查询：在可接受时间内响应（通常30秒-2分钟）

**系统可用性**：
- 系统能够稳定运行，支持日常监控需求
- 提供基本的故障恢复能力
- 数据能够正常采集和存储

### 1.4 目标用户
- **主要用户**：校园网络管理员、IT运维人员
- **次要用户**：网络安全专员、系统管理员
- **决策用户**：信息化部门负责人、CIO

## 2. 用户需求分析

### 2.1 核心用户故事

#### 2.1.1 网络管理员

**用户故事1：定期设备监控**
- **As a** 网络管理员，**I want to** 定期监控所有网络设备状态，**So that** 我能及时发现设备故障和性能问题

**验收标准（简化版）**：
- [ ] 系统能够显示设备的基本状态（在线/离线）
- [ ] 支持50-100台设备的监控
- [ ] 设备离线时能够发出告警
- [ ] 提供基本的设备性能信息显示
- [ ] 设备状态每10分钟更新一次

**用户故事2：安全态势可视化**
- **As a** 网络管理员，**I want to** 通过可视化大屏查看网络安全态势，**So that** 我能快速了解整体安全状况

**验收标准（简化版）**：
- [ ] 能够显示基本的网络拓扑图
- [ ] 显示当前告警数量和设备状态统计
- [ ] 提供基本的安全事件列表
- [ ] 支持常见分辨率的显示

**用户故事3：智能告警管理**
- **As a** 网络管理员，**I want to** 接收智能告警通知，**So that** 我能优先处理重要安全事件

**验收标准（简化版）**：
- [ ] 告警能够按严重程度分级显示
- [ ] 告警能够在界面上正常显示
- [ ] 支持基本的告警确认和处理状态标记
- [ ] 提供告警历史记录查看

#### 2.1.2 安全运维人员

**用户故事4：自然语言安全查询**
- **As a** 安全运维人员，**I want to** 使用自然语言查询安全事件，**So that** 我能快速获取所需信息

**验收标准（简化版）**：
- [ ] 支持基本的中文自然语言查询
- [ ] 能够查询告警信息和设备状态
- [ ] AI查询能够在30秒-2分钟内响应
- [ ] 查询结果能够正常显示
- [ ] 基于OpenAI Agents + DeepSeek V3实现

**用户故事5：AI威胁分析报告**
- **As a** 安全运维人员，**I want to** 获得AI生成的威胁分析报告，**So that** 我能更好地理解和应对安全威胁

**验收标准（简化版）**：
- [ ] AI能够分析安全事件并生成基本报告
- [ ] 报告包含威胁类型和基本建议
- [ ] 支持报告的查看和基本导出功能
- [ ] 提供基本的威胁信息展示
- [ ] 基于DeepSeek V3模型进行分析

**用户故事6：自定义告警规则**
- **As a** 安全运维人员，**I want to** 自定义告警规则，**So that** 我能根据实际需求调整监控策略

**验收标准（简化版）**：
- [ ] 提供基本的规则配置界面
- [ ] 支持简单的告警条件设置（如阈值）
- [ ] 规则能够正常生效
- [ ] 提供基本的规则管理功能

### 2.2 用户痛点
1. **监控盲区**：传统监控工具覆盖不全面，存在监控死角
2. **告警疲劳**：大量误报导致重要告警被忽略
3. **分析复杂**：缺乏智能分析能力，需要人工判断威胁
4. **响应滞后**：发现威胁到响应处置时间过长
5. **运维复杂**：需要专业技能才能有效使用监控系统

## 3. 功能需求规格说明

### 3.1 核心功能模块

#### 3.1.1 网络数据采集模块
**功能描述**：基于SNMP协议采集校园网络设备的基本状态和流量数据

**基本需求**：
- **SNMP数据采集**：
  - 支持SNMP v2c协议
  - 定期采集设备状态和接口流量信息
  - 监控对象：交换机、路由器、无线AP等主要网络设备
- **设备发现**：
  - 支持手动添加设备到监控列表
  - 提供基本的设备状态检测
- **数据存储**：
  - 将采集的数据存储到数据库
  - 提供基本的数据查询功能

**验收标准（简化版）**：
- 能够成功采集设备的基本状态信息
- 支持50-100台设备的监控
- 数据能够正常存储和查询
- 设备状态每10分钟更新一次
- 接口流量每5分钟更新一次

#### 3.1.2 安全检测引擎
**功能描述**：基于基本规则的安全威胁检测和告警

**基本需求**：
- **设备状态异常检测**：
  - 检测设备CPU、内存使用率过高
  - 检测设备离线状态
  - 提供基本的阈值告警
- **简单攻击检测**：
  - 检测明显的端口扫描行为
  - 检测设备连接异常
  - 支持基本的黑名单IP检测
- **告警生成**：
  - 生成基本的安全告警
  - 支持告警级别分类
  - 提供告警历史记录

**验收标准（简化版）**：
- 能够检测基本的设备异常和安全威胁
- 告警能够正常生成和显示
- 支持基本的告警管理功能
- 告警在检测到异常后5-15分钟内生成

#### 3.1.3 智能分析引擎（OpenAI Agents + DeepSeek V3）
**功能描述**：基于OpenAI Agents框架和DeepSeek V3模型的基本AI分析功能

**基本需求**：
- **AI对话功能**：
  - 使用OpenAI Agents框架集成DeepSeek V3模型
  - 支持基本的中文自然语言查询
  - 能够回答设备状态和告警相关问题
- **简单分析功能**：
  - 提供基本的威胁分析和建议
  - 支持告警信息的解释说明
  - 生成简单的分析报告
- **对话界面**：
  - 基于ant-design-x-vue的对话组件
  - 支持基本的问答交互
  - 提供查询历史记录

**验收标准（简化版）**：
- AI能够响应基本的自然语言查询
- 对话界面能够正常工作
- 能够提供基本的分析和建议
- AI查询在30秒-2分钟内响应
- 基于OpenAI Agents框架和DeepSeek V3模型

#### 3.1.4 告警规则引擎
**功能描述**：基本的告警生成和分级处理

**基本需求**：
- **告警分级**：
  - 高危：重要系统异常、明显威胁
  - 中危：可疑行为、配置异常
  - 低危：一般异常、信息提示
- **基本降噪**：
  - 相同类型告警自动合并
  - 支持基本的白名单过滤
- **通知方式**：
  - Web界面显示告警
  - 基本的日志记录

**验收标准（简化版）**：
- 告警能够正常生成和显示
- 支持基本的告警分级
- 具备基本的降噪功能

#### 3.1.5 可视化监控大屏
**功能描述**：基本的网络监控可视化展示

**基本需求**：
- **网络拓扑图**：
  - 显示设备连接关系和基本状态
  - 支持点击查看设备详情
  - 基本的状态指示（正常/异常/离线）
- **监控仪表盘**：
  - 显示在线设备数、告警数量等基本指标
  - 提供简单的统计图表
- **告警列表**：
  - 显示当前告警信息
  - 支持基本的告警查看和确认
- **基本界面**：
  - 支持常见分辨率显示
  - 提供基本的数据导出功能

**验收标准（简化版）**：
- 界面能够正常加载和显示
- 数据能够正常刷新
- 支持基本的用户交互
- 页面首次加载≤10秒，后续加载≤5秒
- 基于Vue.js + ECharts + G6技术栈实现

#### 3.1.6 自定义告警规则配置模块
**功能描述**：基本的告警规则配置功能

**基本需求**：
- **规则配置界面**：
  - 提供简单的规则配置表单
  - 支持基本的条件设置（如阈值）
  - 规则启用/禁用功能
- **基本规则管理**：
  - 规则列表查看和编辑
  - 简单的规则分组
  - 基本的规则测试功能
- **规则模板**：
  - 提供几个常用的规则模板
  - 支持基于模板创建新规则

**验收标准（简化版）**：
- 规则配置界面能够正常工作
- 规则能够正常生效
- 支持基本的规则管理功能

#### 3.1.7 AI对话界面模块（ant-design-x-vue）
**功能描述**：基于ant-design-x-vue的基本AI对话功能

**基本需求**：
- **对话界面**：
  - 使用ant-design-x-vue的Conversations组件
  - 支持基本的文本输入和显示
  - 简单的消息气泡界面
- **基本对话功能**：
  - 支持用户输入和AI回复
  - 保存基本的对话历史
  - 简单的会话管理
- **基本交互**：
  - 支持常用查询的快捷按钮
  - 基本的查询历史记录
  - 简单的结果展示

**验收标准（简化版）**：
- 对话界面能够正常工作
- AI能够响应基本查询
- 支持基本的对话历史功能
- 基于ant-design-x-vue组件实现
- AI响应时间在30秒-2分钟内

### 3.2 系统管理功能（简化版）

#### 3.2.1 设备管理模块
- **基本设备管理**：设备IP地址、类型、状态管理
- **简单配置管理**：基本的SNMP配置和监控参数
- **设备分组**：简单的设备分组功能

#### 3.2.2 配置管理模块
- **基本配置**：系统基本参数设置
- **简单备份**：配置文件的基本备份功能

### 3.3 外部集成功能模块（简化版）

#### 3.3.1 威胁情报集成模块
**功能描述**：基本的威胁情报查询功能

**基本需求**：
- **基本威胁情报**：
  - 内置基本的恶意IP黑名单
  - 支持简单的威胁情报查询
- **基本集成**：
  - 支持基本的威胁情报API调用
  - 简单的威胁情报缓存

**验收标准（简化版）**：
- 威胁情报查询能够正常工作
- 支持基本的黑名单检测

#### 3.3.2 系统监控和健康检查模块（简化版）
**功能描述**：基本的系统状态监控

**基本需求**：
- **基本监控**：系统CPU、内存使用情况
- **健康检查**：数据库连接状态检查
- **简单告警**：系统异常时的基本告警

**验收标准（简化版）**：
- 系统状态能够正常显示
- 基本的健康检查功能正常

#### 3.3.3 数据备份模块（简化版）
**功能描述**：基本的数据备份功能

**基本需求**：
- **简单备份**：数据库的基本备份功能
- **配置备份**：系统配置的简单备份

**验收标准（简化版）**：
- 备份功能能够正常工作
- 支持基本的数据恢复

## 4. 非功能性需求

### 4.1 基本性能要求

#### 4.1.1 响应时间（明确标准）
- **页面加载**：首次加载≤10秒，后续加载≤5秒
- **数据查询**：基础查询≤30秒，复杂查询≤60秒
- **告警生成**：异常检测后5-15分钟内生成告警
- **AI查询**：自然语言查询30秒-2分钟内响应

#### 4.1.2 基本处理能力
- **设备监控**：支持50-100台设备的基本监控
- **数据存储**：能够存储基本的监控数据和告警历史
- **用户访问**：支持单用户访问，满足基本使用需求
- **AI功能**：DeepSeek V3 API能够正常调用和响应
### 4.2 基本可用性需求
- **系统稳定性**：系统能够稳定运行，支持日常监控需求
- **数据备份**：提供基本的数据备份功能
- **故障恢复**：系统故障后能够恢复正常运行
- **基本容错**：具备基本的错误处理和恢复能力

### 4.3 基本安全需求
- **数据安全**：确保监控数据的基本安全性
- **API安全**：DeepSeek API密钥的安全存储
- **访问控制**：基本的用户访问控制（单用户模式）
- **威胁检测**：能够检测基本的安全威胁

### 4.4 基本兼容性需求
- **浏览器支持**：支持主流浏览器（Chrome、Firefox、Edge）
- **设备兼容**：支持常见网络设备的SNMP监控
- **技术栈**：
  - Python 3.13.2
  - Node.js 20.19+
  - MySQL 8.0
  - OpenAI Agents + DeepSeek V3
  - loguru==0.7.3

### 4.5 基本维护需求
- **日志记录**：使用loguru进行基本的日志记录
- **配置管理**：基本的配置文件管理
- **错误处理**：基本的错误处理和日志记录
- **系统监控**：基本的系统状态监控

## 5. 技术架构和实现方案

### 5.1 系统架构设计

#### 5.1.1 完整系统架构图

智能校园网络安全监控平台采用八层架构设计，通过清晰的层次划分和模块化组件，实现了高可扩展性和可维护性。

```mermaid
graph TB
    %% 网络设备层
    subgraph "网络设备层 (Network Device Layer)"
        SW[交换机<br/>Switch<br/>SNMP v2c]
        RT[路由器<br/>Router<br/>SNMP v2c]
        AP[无线AP<br/>Wireless AP<br/>SNMP v2c]
        SV[服务器<br/>Server<br/>System Monitor]
        FW[防火墙<br/>Firewall<br/>Log Export]
    end

    %% 数据采集层
    subgraph "数据采集层 (Data Collection Layer)"
        SNMP[SNMP采集器<br/>SNMP Collector<br/>pysnmp>=6.0.0]
        SCAN[网络扫描器<br/>Network Scanner<br/>python3-nmap>=1.9.0]
        MON[流量监控器<br/>Traffic Monitor<br/>Interface Statistics]
        LOG[日志采集器<br/>Log Collector<br/>Syslog/File]
    end

    %% 数据处理层
    subgraph "数据处理层 (Data Processing Layer)"
        CLEAN[数据清洗<br/>Data Cleaning<br/>pandas 2.3.0]
        RULE[规则引擎<br/>Rule Engine<br/>Threshold Detection]
        DETECT[异常检测<br/>Anomaly Detection<br/>Statistical Analysis]
        PARSE[日志解析<br/>Log Parser<br/>Pattern Matching]
    end

    %% 智能分析层
    subgraph "智能分析层 (AI Analysis Layer)"
        AGENT_SEC[安全分析Agent<br/>Security Analysis Agent<br/>DeepSeek V3]
        AGENT_OPS[运维助手Agent<br/>Operations Assistant Agent<br/>Natural Language]
        AGENT_ALERT[告警解释Agent<br/>Alert Explanation Agent<br/>AI-powered Insights]
        AGENT_CHAT[对话式查询<br/>Conversational Query<br/>Voice Interaction]
    end

    %% 数据存储层
    subgraph "数据存储层 (Data Storage Layer)"
        MYSQL[(MySQL 8.0<br/>Unified Storage<br/>Device/Alert/Config/Cache)]
        FILES[文件存储<br/>File Storage<br/>Logs/Reports/Backups]
    end

    %% 应用服务层
    subgraph "应用服务层 (Application Service Layer)"
        API[FastAPI后端<br/>Backend API<br/>Python 3.13.2]
        WS[WebSocket服务<br/>WebSocket Service<br/>Real-time Push]
        GATEWAY[API网关<br/>API Gateway<br/>Rate Limiting]
    end

    %% 用户界面层
    subgraph "用户界面层 (User Interface Layer)"
        WEB[Web监控界面<br/>Web Dashboard<br/>Vue.js 3.5.0]
        DASH[监控大屏<br/>Monitoring Screen<br/>ECharts 5.6.0]
        MGMT[系统管理界面<br/>System Management<br/>Element Plus 2.9.0]
        MOBILE[移动端<br/>Mobile App<br/>Responsive Design]
    end

    %% 告警通知层
    subgraph "告警通知层 (Alert Notification Layer)"
        ALERT[告警管理器<br/>Alert Manager<br/>Priority Queue]
        WEB_NOTIFY[Web通知<br/>Web Notification<br/>Browser Popup]
        LOG_NOTIFY[日志记录<br/>Log Notification<br/>System Logs]
    end

    %% 外部集成层
    subgraph "外部集成层 (External Integration Layer)"
        THREAT[威胁情报<br/>Threat Intelligence<br/>Static Blacklist]
        NTP[时间同步<br/>NTP Service<br/>Time Synchronization]
        BACKUP[备份服务<br/>Backup Service<br/>Data Protection]
        MONITOR[系统监控<br/>System Monitoring<br/>Health Check]
    end

    %% 数据流连接
    SW --> SNMP
    RT --> SNMP
    AP --> SNMP
    SV --> SCAN
    FW --> LOG

    SNMP --> CLEAN
    SCAN --> CLEAN
    MON --> CLEAN
    LOG --> PARSE

    CLEAN --> RULE
    PARSE --> RULE
    RULE --> DETECT

    DETECT --> AGENT_SEC
    AGENT_SEC --> AGENT_OPS
    AGENT_OPS --> AGENT_ALERT
    AGENT_ALERT --> AGENT_CHAT

    AGENT_CHAT --> MYSQL
    MYSQL --> FILES

    MYSQL --> API
    FILES --> API
    API --> WS
    API --> GATEWAY

    GATEWAY --> WEB
    WS --> WEB
    WS --> DASH
    GATEWAY --> MGMT
    WEB --> MOBILE

    DETECT --> ALERT
    AGENT_ALERT --> ALERT
    ALERT --> WEB_NOTIFY
    ALERT --> LOG_NOTIFY

    RULE --> THREAT
    API --> NTP
    FILES --> BACKUP
    API --> MONITOR

    WEB -.-> AGENT_CHAT
    MGMT -.-> AGENT_SEC

    %% 样式定义
    classDef deviceLayer fill:#e3f2fd,stroke:#1976d2,stroke-width:2px,color:#000
    classDef collectLayer fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px,color:#000
    classDef processLayer fill:#e8f5e8,stroke:#388e3c,stroke-width:2px,color:#000
    classDef aiLayer fill:#fce4ec,stroke:#c2185b,stroke-width:2px,color:#000
    classDef storageLayer fill:#f1f8e9,stroke:#689f38,stroke-width:2px,color:#000
    classDef serviceLayer fill:#e0f2f1,stroke:#00796b,stroke-width:2px,color:#000
    classDef uiLayer fill:#e8eaf6,stroke:#3f51b5,stroke-width:2px,color:#000
    classDef alertLayer fill:#ffebee,stroke:#d32f2f,stroke-width:2px,color:#000
    classDef externalLayer fill:#f5f5f5,stroke:#616161,stroke-width:2px,color:#000

    class SW,RT,AP,SV,FW deviceLayer
    class SNMP,SCAN,MON,LOG collectLayer
    class CLEAN,RULE,DETECT,PARSE processLayer
    class AGENT_SEC,AGENT_OPS,AGENT_ALERT,AGENT_CHAT aiLayer
    class MYSQL,FILES storageLayer
    class API,WS,GATEWAY serviceLayer
    class WEB,DASH,MGMT,MOBILE uiLayer
    class ALERT,WEB_NOTIFY,LOG_NOTIFY alertLayer
    class THREAT,NTP,BACKUP,MONITOR externalLayer
```

#### 5.1.2 架构层次说明
- **网络设备层**：交换机、路由器、无线AP、服务器、防火墙
- **数据采集层**：SNMP采集器、网络扫描器、流量监控器、日志采集器
- **数据处理层**：数据清洗、规则引擎、异常检测、日志解析
- **智能分析层**：openai-agents框架 + DeepSeek V3模型
- **数据存储层**：MySQL数据库 + 文件存储
- **应用服务层**：FastAPI后端、WebSocket服务、API网关
- **用户界面层**：Vue.js Web界面、监控大屏、系统管理界面、移动端
- **告警通知层**：多渠道告警通知机制
- **外部集成层**：威胁情报、时间同步、备份服务、系统监控

### 5.2 技术栈规格

#### 5.2.1 后端技术栈
- **编程语言**：Python 3.13.2
- **依赖管理**：pip + requirements.txt
- **虚拟环境**：venv（强制使用）
- **Web框架**：FastAPI 0.115.12
- **数据库**：MySQL 8.0
- **网络协议库**：
  - pysnmp>=6.0.0（SNMP协议实现）
  - python3-nmap>=1.9.0（网络扫描）
  - psutil>=7.0.0（系统监控）
- **AI分析引擎**：
  - openai-agents 0.1.0
  - DeepSeek V3 API（模型名称:deepseek-chat）
- **数据处理**：
  - pandas>=2.3.0（数据分析）
  - numpy>=2.0.0（数值计算）

#### 5.2.2 前端技术栈
- **前端框架**：Vue.js ^3.5.0
- **UI组件库**：Element Plus ^2.9.0
- **AI对话组件**：ant-design-x-vue ^1.2.7
- **图表库**：ECharts ^5.6.0
- **网络拓扑图**：@antv/g6 ^5.0.0
- **样式框架**：Tailwind CSS ^4.1.11
- **构建工具**：Vite ^7.0.0
- **状态管理**：Pinia ^3.0.3
- **路由管理**：Vue Router ^4.5.1

#### 5.2.3 前端package.json配置
```json
{
  "name": "campusguard-frontend",
  "version": "1.0.0",
  "type": "module",
  "scripts": {
    "dev": "vite",
    "build": "vue-tsc && vite build",
    "preview": "vite preview"
  },
  "dependencies": {
    "vue": "^3.5.0",
    "vue-router": "^4.5.1",
    "pinia": "^3.0.3",
    "element-plus": "^2.9.0",
    "ant-design-x-vue": "^1.2.7",
    "@antv/g6": "^5.0.0",
    "echarts": "^5.6.0",
    "vue-echarts": "^7.0.4",
    "axios": "^1.8.4",
    "@element-plus/icons-vue": "^2.3.1"
  },
  "devDependencies": {
    "@vitejs/plugin-vue": "^5.0.1",
    "vite": "^7.0.0",
    "unplugin-vue-components": "^0.27.0",
    "unplugin-auto-import": "^0.18.0",
    "@types/node": "^22.10.5",
    "typescript": "^5.7.3",
    "vue-tsc": "^2.1.10",
    "tailwindcss": "^4.1.11"
  }
}

#### 5.2.4 G6网络拓扑图基础实现
```vue
<template>
  <div class="network-topology">
    <div ref="graphContainer" class="graph-container"></div>
    <el-button @click="refreshTopology">刷新拓扑</el-button>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { Graph } from '@antv/g6'

const graphContainer = ref<HTMLDivElement>()
let graph: Graph | null = null

const initGraph = () => {
  if (!graphContainer.value) return

  graph = new Graph({
    container: graphContainer.value,
    width: 800,
    height: 600,
    behaviors: ['zoom-canvas', 'drag-canvas'],
    layout: {
      type: 'force',
      preventOverlap: true
    },
    node: {
      style: {
        size: 40,
        fill: '#5B8FF9',
        labelText: (d: any) => d.label
      }
    },
    edge: {
      style: {
        stroke: '#e2e2e2',
        lineWidth: 2
      }
    }
  })

  loadNetworkData()
}

const loadNetworkData = async () => {
  try {
    const response = await fetch('/api/v1/topology/data')
    const data = await response.json()

    const nodes = data.devices.map((device: any) => ({
      id: device.id,
      label: device.name,
      style: {
        fill: device.status === 'online' ? '#52c41a' : '#ff4d4f'
      }
    }))

    const edges = data.connections.map((conn: any) => ({
      id: `${conn.source}-${conn.target}`,
      source: conn.source,
      target: conn.target
    }))

    graph?.setData({ nodes, edges })
    graph?.render()
  } catch (error) {
    console.error('加载网络拓扑数据失败:', error)
  }
}

const refreshTopology = async () => {
  await loadNetworkData()
}

onMounted(() => {
  initGraph()
})
</script>

<style scoped>
.network-topology {
  width: 100%;
  height: 600px;
}

.graph-container {
  width: 100%;
  height: 100%;
  border: 1px solid #e8e8e8;
}
</style>
```

#### 5.2.5 开发环境要求
- **Python环境**：Python 3.13.2 + venv虚拟环境
- **Node.js环境**：Node.js 20.19+
- **数据库**：MySQL 8.0+

#### 5.2.6 环境变量配置(.env)
```bash
# 数据库配置
DATABASE_URL=mysql+pymysql://campusguard:your_password@localhost:3306/campusguard
MYSQL_HOST=localhost
MYSQL_PORT=3306
MYSQL_DATABASE=campusguard
MYSQL_USER=campusguard
MYSQL_PASSWORD=your_password

# DeepSeek AI配置
DEEPSEEK_API_KEY=***********************************
DEEPSEEK_BASE_URL=https://api.deepseek.com/v1
DEEPSEEK_MODEL=deepseek-chat

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=./logs/campusguard.log
```

#### 5.2.7 requirements.txt
```txt
# Web框架
fastapi>=0.115.12
uvicorn[standard]>=0.32.0

# 数据库
PyMySQL>=1.1.0
SQLAlchemy>=2.0.0

# 网络协议
pysnmp>=6.0.0
python3-nmap>=1.9.0
psutil>=7.0.0

# 数据处理
pandas>=2.3.0
numpy>=2.0.0

# AI分析
openai-agents>=0.1.0
httpx>=0.28.0
pydantic>=2.10.0

# 配置管理
python-dotenv>=1.1.0

# 日志
loguru>=0.7.3

```

### 5.3 数据库设计

#### 5.3.1 MySQL 8.0 核心表结构
- **设备信息表**：devices（设备基本信息）
- **监控数据表**：monitoring_data（设备性能数据）
- **告警记录表**：alerts（告警事件记录）
- **告警规则表**：alert_rules（告警规则配置）
- **AI分析历史表**：ai_analysis_history（AI分析记录）
- **系统配置表**：system_configs（系统配置参数）

#### 5.3.2 SQLAlchemy数据模型示例
```python
from sqlalchemy import Column, Integer, String, DateTime, Text, Float, ForeignKey
from sqlalchemy.ext.declarative import declarative_base
from datetime import datetime

Base = declarative_base()

class Device(Base):
    __tablename__ = "devices"

    id = Column(Integer, primary_key=True)
    name = Column(String(100), nullable=False)
    ip_address = Column(String(45), nullable=False, unique=True)
    device_type = Column(String(50), nullable=False)
    location = Column(String(100))
    status = Column(String(20), default="active")
    created_at = Column(DateTime, default=datetime.utcnow)

class MonitoringData(Base):
    __tablename__ = "monitoring_data"

    id = Column(Integer, primary_key=True)
    device_id = Column(Integer, ForeignKey('devices.id'), nullable=False)
    metric_name = Column(String(100), nullable=False)
    metric_value = Column(Float, nullable=False)
    timestamp = Column(DateTime, default=datetime.utcnow)

class Alert(Base):
    __tablename__ = "alerts"

    id = Column(Integer, primary_key=True)
    device_id = Column(Integer, ForeignKey('devices.id'))
    severity = Column(String(20), nullable=False)
    title = Column(String(200), nullable=False)
    description = Column(Text)
    deepseek_analysis = Column(Text)
    status = Column(String(20), default="open")
    created_at = Column(DateTime, default=datetime.utcnow)

class AlertRule(Base):
    __tablename__ = "alert_rules"

    id = Column(Integer, primary_key=True)
    name = Column(String(100), nullable=False)
    condition = Column(Text, nullable=False)
    severity = Column(String(20), nullable=False)
    enabled = Column(String(10), default="true")
    created_at = Column(DateTime, default=datetime.utcnow)
```

### 5.4 API接口设计

#### 5.4.1 核心业务API
- **设备管理API**：
  - GET/POST /api/v1/devices
  - GET /api/v1/devices/{device_id}/status
  - PUT /api/v1/devices/{device_id}
  - DELETE /api/v1/devices/{device_id}
- **监控数据API**：
  - GET /api/v1/monitoring/data
  - GET /api/v1/monitoring/realtime/{device_id}
  - GET /api/v1/monitoring/topology
- **告警管理API**：
  - GET/POST /api/v1/alerts
  - POST /api/v1/alerts/{alert_id}/acknowledge
  - PUT /api/v1/alerts/{alert_id}/status
- **告警规则API**：
  - GET/POST /api/v1/alert-rules
  - PUT /api/v1/alert-rules/{rule_id}
  - DELETE /api/v1/alert-rules/{rule_id}
- **AI分析API**：
  - POST /api/v1/ai/analyze
  - POST /api/v1/ai/chat
- **系统配置API**：
  - GET/PUT /api/v1/system/config

#### 5.4.2 AI分析API示例

**威胁分析API**
```python
# POST /api/v1/ai/analyze
{
  "analysis_type": "security_event",
  "data_source": {
    "device_ids": ["device_001"],
    "alert_ids": [123, 456]
  }
}

# 响应
{
  "status": "success",
  "data": {
    "analysis_result": "检测到异常网络流量...",
    "threat_level": "medium",
    "recommendations": ["建议检查防火墙规则"]
  }
}
            agent_config:
              type: object
              properties:
                temperature:
                  type: number
                  minimum: 0.0
                  maximum: 2.0
                  default: 0.7
                max_tokens:
                  type: integer
                  minimum: 100
                  maximum: 8192
                  default: 4096
                priority:
                  type: string
                  enum: [low, normal, high, urgent]
                  default: normal
        example:
          analysis_type: "security_event"
          data_source:
            alert_ids: [1001, 1002, 1003]
            time_range:
              start_time: "2024-01-01T00:00:00Z"
              end_time: "2024-01-01T23:59:59Z"
          analysis_options:
            depth: "comprehensive"
            include_recommendations: true
            correlation_analysis: true
          agent_config:
            temperature: 0.6
            priority: "high"

  responses:
    200:
      description: 分析成功
      content:
        application/json:
          schema:
            type: object
            properties:
              success:
                type: boolean
                example: true
              request_id:
                type: string
                description: 请求唯一标识
              analysis_result:
                type: object
                properties:
                  summary:
                    type: string
                    description: 分析摘要
                  threat_level:
                    type: string
                    enum: [low, medium, high, critical]
                  confidence_score:
                    type: number
                    minimum: 0.0
                    maximum: 1.0
                    description: 置信度分数
                  findings:
                    type: array
                    items:
                      type: object
                      properties:
                        category:
                          type: string
                        description:
                          type: string
                        severity:
                          type: string
                        evidence:
                          type: array
                          items:
                            type: string
                  recommendations:
                    type: array
                    items:
                      type: object
                      properties:
                        action:
                          type: string
                        priority:
                          type: string
                        estimated_effort:
                          type: string
                        expected_impact:
                          type: string
                  correlation_analysis:
                    type: object
                    properties:
                      related_events:
                        type: array
                        items:
                          type: object
                      attack_chain:
                        type: array
                        items:
                          type: string
                      timeline:
                        type: array
                        items:
                          type: object
              metadata:
                type: object
                properties:
                  agent_type:
                    type: string
                  model_version:
                    type: string
                  processing_time:
                    type: number
                    description: 处理时间（秒）
                  tokens_used:
                    type: integer
                  cost_estimate:
                    type: number
              timestamp:
                type: string
                format: date-time

    400:
      description: 请求参数错误
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'

    429:
      description: 请求频率限制
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/RateLimitError'

    500:
      description: 服务器内部错误
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ServerError'
```

********* AI对话API**
```yaml
POST /api/v1/agents/chat:
  summary: AI对话接口
  description: 与CampusGuard AI助手进行自然语言对话
  requestBody:
    required: true
    content:
      application/json:
        schema:
          type: object
          required: [message]
          properties:
            message:
              type: string
              minLength: 1
              maxLength: 4000
              description: 用户消息内容
            agent_type:
              type: string
              enum: [security_analyst, operations_assistant, alert_explainer]
              default: operations_assistant
              description: Agent类型
            conversation_id:
              type: string
              description: 会话ID，用于多轮对话
            context:
              type: object
              properties:
                current_alerts:
                  type: array
                  items:
                    type: integer
                  description: 当前关注的告警ID
                device_focus:
                  type: array
                  items:
                    type: string
                  description: 当前关注的设备ID
                time_context:
                  type: string
                  description: 时间上下文
            options:
              type: object
              properties:
                stream:
                  type: boolean
                  default: false
                  description: 是否流式返回
                include_sources:
                  type: boolean
                  default: true
                  description: 是否包含数据源信息
                max_history:
                  type: integer
                  default: 10
                  description: 最大历史消息数
        example:
          message: "今天有哪些高危告警需要处理？"
          agent_type: "security_analyst"
          conversation_id: "conv_20240101_001"
          context:
            time_context: "today"
          options:
            include_sources: true

  responses:
    200:
      description: 对话成功
      content:
        application/json:
          schema:
            type: object
            properties:
              success:
                type: boolean
              conversation_id:
                type: string
              response:
                type: object
                properties:
                  content:
                    type: string
                    description: AI回复内容
                  agent_type:
                    type: string
                  confidence:
                    type: number
                    minimum: 0.0
                    maximum: 1.0
                  sources:
                    type: array
                    items:
                      type: object
                      properties:
                        type:
                          type: string
                          enum: [device_data, alert_data, threat_intel, knowledge_base]
                        reference:
                          type: string
                        timestamp:
                          type: string
                          format: date-time
                  actions:
                    type: array
                    items:
                      type: object
                      properties:
                        type:
                          type: string
                          enum: [view_details, export_report, create_ticket, acknowledge_alert]
                        label:
                          type: string
                        data:
                          type: object
              metadata:
                type: object
                properties:
                  processing_time:
                    type: number
                  tokens_used:
                    type: integer
                  tools_called:
                    type: array
                    items:
                      type: string
              timestamp:
                type: string
                format: date-time

        application/x-ndjson:
          description: 流式响应格式
          schema:
            type: object
            properties:
              type:
                type: string
                enum: [chunk, complete, error]
              data:
                type: string
              metadata:
                type: object
```

********* 统一错误处理机制**
```yaml
components:
  schemas:
    ErrorResponse:
      type: object
      required: [success, error]
      properties:
        success:
          type: boolean
          example: false
        error:
          type: object
          required: [code, message]
          properties:
            code:
              type: string
              enum: [
                INVALID_REQUEST,
                MISSING_PARAMETER,
                INVALID_PARAMETER,
                AUTHENTICATION_FAILED,
                AUTHORIZATION_FAILED,
                RESOURCE_NOT_FOUND,
                AGENT_ERROR,
                MODEL_ERROR,
                TIMEOUT_ERROR,
                RATE_LIMIT_EXCEEDED,
                INTERNAL_ERROR
              ]
            message:
              type: string
              description: 错误描述信息
            details:
              type: object
              description: 详细错误信息
              properties:
                field:
                  type: string
                  description: 错误字段名
                validation_errors:
                  type: array
                  items:
                    type: object
                    properties:
                      field:
                        type: string
                      message:
                        type: string
                      value:
                        type: string
                retry_after:
                  type: integer
                  description: 重试等待时间（秒）
        request_id:
          type: string
          description: 请求唯一标识
        timestamp:
          type: string
          format: date-time
        suggestion:
          type: string
          description: 解决建议

    RateLimitError:
      allOf:
        - $ref: '#/components/schemas/ErrorResponse'
        - type: object
          properties:
            rate_limit:
              type: object
              properties:
                limit:
                  type: integer
                  description: 限制次数
                remaining:
                  type: integer
                  description: 剩余次数
                reset_time:
                  type: string
                  format: date-time
                  description: 重置时间
                window:
                  type: integer
                  description: 时间窗口（秒）

    ServerError:
      allOf:
        - $ref: '#/components/schemas/ErrorResponse'
        - type: object
          properties:
            error_id:
              type: string
              description: 错误追踪ID
            support_contact:
              type: string
              description: 技术支持联系方式
```

********* 数据格式转换规范**
```python
# 数据转换器实现
from typing import Any, Dict, List, Optional, Union
from datetime import datetime
from pydantic import BaseModel, Field, validator
from enum import Enum

class AgentType(str, Enum):
    """Agent类型枚举"""
    SECURITY_ANALYST = "security_analyst"
    OPERATIONS_ASSISTANT = "operations_assistant"
    ALERT_EXPLAINER = "alert_explainer"

class AnalysisType(str, Enum):
    """分析类型枚举"""
    SECURITY_EVENT = "security_event"
    NETWORK_ANOMALY = "network_anomaly"
    DEVICE_BEHAVIOR = "device_behavior"
    THREAT_CORRELATION = "threat_correlation"

class ThreatLevel(str, Enum):
    """威胁级别枚举"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

class Priority(str, Enum):
    """优先级枚举"""
    LOW = "low"
    NORMAL = "normal"
    HIGH = "high"
    URGENT = "urgent"

# 请求模型
class ThreatAnalysisRequest(BaseModel):
    """威胁分析请求模型"""
    analysis_type: AnalysisType
    data_source: Dict[str, Any]
    analysis_options: Optional[Dict[str, Any]] = Field(default_factory=dict)
    agent_config: Optional[Dict[str, Any]] = Field(default_factory=dict)

    @validator('data_source')
    def validate_data_source(cls, v):
        required_fields = ['device_ids', 'alert_ids', 'time_range', 'custom_query']
        if not any(field in v for field in required_fields):
            raise ValueError('data_source must contain at least one of: device_ids, alert_ids, time_range, custom_query')
        return v

class ChatRequest(BaseModel):
    """对话请求模型"""
    message: str = Field(..., min_length=1, max_length=4000)
    agent_type: AgentType = AgentType.OPERATIONS_ASSISTANT
    conversation_id: Optional[str] = None
    context: Optional[Dict[str, Any]] = Field(default_factory=dict)
    options: Optional[Dict[str, Any]] = Field(default_factory=dict)

# 响应模型
class Finding(BaseModel):
    """发现项模型"""
    category: str
    description: str
    severity: str
    evidence: List[str] = Field(default_factory=list)

class Recommendation(BaseModel):
    """建议模型"""
    action: str
    priority: str
    estimated_effort: str
    expected_impact: str

class CorrelationAnalysis(BaseModel):
    """关联分析模型"""
    related_events: List[Dict[str, Any]] = Field(default_factory=list)
    attack_chain: List[str] = Field(default_factory=list)
    timeline: List[Dict[str, Any]] = Field(default_factory=list)

class AnalysisResult(BaseModel):
    """分析结果模型"""
    summary: str
    threat_level: ThreatLevel
    confidence_score: float = Field(..., ge=0.0, le=1.0)
    findings: List[Finding] = Field(default_factory=list)
    recommendations: List[Recommendation] = Field(default_factory=list)
    correlation_analysis: Optional[CorrelationAnalysis] = None

class ResponseMetadata(BaseModel):
    """响应元数据模型"""
    agent_type: str
    model_version: str
    processing_time: float
    tokens_used: int
    cost_estimate: float

class ThreatAnalysisResponse(BaseModel):
    """威胁分析响应模型"""
    success: bool = True
    request_id: str
    analysis_result: AnalysisResult
    metadata: ResponseMetadata
    timestamp: datetime = Field(default_factory=datetime.utcnow)

class Source(BaseModel):
    """数据源模型"""
    type: str
    reference: str
    timestamp: datetime

class Action(BaseModel):
    """操作模型"""
    type: str
    label: str
    data: Dict[str, Any] = Field(default_factory=dict)

class ChatResponse(BaseModel):
    """对话响应模型"""
    success: bool = True
    conversation_id: str
    response: Dict[str, Any]
    metadata: Dict[str, Any]
    timestamp: datetime = Field(default_factory=datetime.utcnow)

# 数据转换器类
class DataConverter:
    """数据格式转换器"""

    @staticmethod
    def convert_db_device_to_api(device_data: Dict) -> Dict[str, Any]:
        """数据库设备数据转换为API格式"""
        return {
            "device_id": device_data.get("id"),
            "device_name": device_data.get("name"),
            "ip_address": device_data.get("ip_address"),
            "device_type": device_data.get("device_type"),
            "status": device_data.get("status"),
            "location": device_data.get("location"),
            "last_seen": device_data.get("updated_at").isoformat() if device_data.get("updated_at") else None,
            "vendor": device_data.get("vendor"),
            "model": device_data.get("model")
        }

    @staticmethod
    def convert_db_alert_to_api(alert_data: Dict) -> Dict[str, Any]:
        """数据库告警数据转换为API格式"""
        return {
            "alert_id": alert_data.get("id"),
            "title": alert_data.get("title"),
            "description": alert_data.get("description"),
            "severity": alert_data.get("severity"),
            "alert_type": alert_data.get("alert_type"),
            "status": alert_data.get("status"),
            "created_at": alert_data.get("created_at").isoformat() if alert_data.get("created_at") else None,
            "updated_at": alert_data.get("updated_at").isoformat() if alert_data.get("updated_at") else None,
            "device_id": alert_data.get("device_id"),
            "ai_analysis": alert_data.get("deepseek_analysis")
        }

    @staticmethod
    def convert_agent_response_to_api(agent_result: Any, agent_type: str) -> Dict[str, Any]:
        """Agent响应转换为API格式"""
        if hasattr(agent_result, 'final_output'):
            content = agent_result.final_output
        else:
            content = str(agent_result)

        # 提取工具调用信息
        tools_called = []
        if hasattr(agent_result, 'new_items'):
            for item in agent_result.new_items:
                if hasattr(item, 'tool_name'):
                    tools_called.append(item.tool_name)

        return {
            "content": content,
            "agent_type": agent_type,
            "confidence": 0.85,  # 默认置信度
            "sources": [],  # 需要根据实际工具调用结果填充
            "actions": [],  # 需要根据内容生成建议操作
            "tools_called": tools_called
        }

    @staticmethod
    def extract_error_details(error: Exception) -> Dict[str, Any]:
        """提取错误详细信息"""
        error_details = {
            "error_type": type(error).__name__,
            "error_message": str(error),
            "timestamp": datetime.utcnow().isoformat()
        }

        # 根据错误类型添加特定信息
        if hasattr(error, 'response'):
            error_details["http_status"] = getattr(error.response, 'status_code', None)

        if hasattr(error, 'code'):
            error_details["error_code"] = error.code

        return error_details

# 错误处理装饰器
def handle_api_errors(func):
    """API错误处理装饰器"""
    async def wrapper(*args, **kwargs):
        try:
            return await func(*args, **kwargs)
        except ValueError as e:
            return {
                "success": False,
                "error": {
                    "code": "INVALID_PARAMETER",
                    "message": str(e),
                    "details": DataConverter.extract_error_details(e)
                },
                "timestamp": datetime.utcnow().isoformat()
            }
        except TimeoutError as e:
            return {
                "success": False,
                "error": {
                    "code": "TIMEOUT_ERROR",
                    "message": "请求处理超时",
                    "details": DataConverter.extract_error_details(e)
                },
                "timestamp": datetime.utcnow().isoformat(),
                "suggestion": "请稍后重试，或简化请求内容"
            }
        except Exception as e:
            return {
                "success": False,
                "error": {
                    "code": "INTERNAL_ERROR",
                    "message": "服务器内部错误",
                    "details": DataConverter.extract_error_details(e)
                },
                "timestamp": datetime.utcnow().isoformat(),
                "suggestion": "请联系技术支持"
            }
    return wrapper
```

#### 5.4.3 外部集成API
- **威胁情报API**：
  - GET /api/v1/threat/intelligence/{ip}
  - POST /api/v1/threat/intelligence/batch/check
  - PUT /api/v1/threat/intelligence/update
- **系统监控API**：
  - GET /api/v1/system/health
  - GET /api/v1/system/metrics
  - GET /api/v1/system/status
- **备份管理API**：
  - POST /api/v1/backup/create
  - GET /api/v1/backup/list
  - POST /api/v1/backup/{backup_id}/restore
- **移动端API**：
  - GET /api/v1/mobile/dashboard
  - GET /api/v1/mobile/alerts/summary
  - POST /api/v1/mobile/push/register

#### 5.4.4 实时数据API
- **WebSocket连接**：
  - WS /ws/realtime/monitoring
  - WS /ws/alerts/notifications
  - WS /ws/system/status
- **实时数据查询API**：
  - GET /api/v1/realtime/metrics/{device_id}
  - GET /api/v1/realtime/alerts/latest
  - GET /api/v1/realtime/system/status

#### 5.4.5 FastAPI核心实现示例

**主应用入口**：
```python
from fastapi import FastAPI, Depends, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.gzip import GZipMiddleware
from sqlalchemy.orm import Session
import uvicorn

app = FastAPI(
    title="CampusGuard API",
    description="校园网络安全监控平台API",
    version="1.0.0",
    docs_url="/api/docs",
    redoc_url="/api/redoc"
)

# 中间件配置
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000"],
    allow_credentials=False,
    allow_methods=["*"],
    allow_headers=["*"],
)
app.add_middleware(GZipMiddleware, minimum_size=1000)

# 数据库依赖
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

# 设备管理API示例
@app.get("/api/v1/devices", response_model=List[DeviceResponse])
async def get_devices(
    page: int = 1,
    size: int = 20,
    device_type: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """获取设备列表"""
    query = db.query(Device)
    if device_type:
        query = query.filter(Device.device_type == device_type)

    devices = query.offset((page - 1) * size).limit(size).all()
    return devices

# WebSocket实时推送
@app.websocket("/ws/realtime/monitoring")
async def websocket_monitoring(websocket: WebSocket):
    await websocket.accept()
    try:
        while True:
            # 获取实时监控数据
            data = await get_realtime_monitoring_data()
            await websocket.send_json(data)
            await asyncio.sleep(5)  # 5秒推送一次
    except WebSocketDisconnect:
        pass

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000)
```

## 6. 详细技术规格

### 6.1 OpenAI Agents + DeepSeek V3 集成实现

#### 6.1.1 基础集成配置

```python
from agents import Agent, Runner, function_tool
from loguru import logger
import os

# 配置DeepSeek V3客户端
deepseek_client = {
    "api_key": os.getenv("DEEPSEEK_API_KEY"),
    "base_url": "https://api.deepseek.com/v1",
    "model": "deepseek-chat"
}
# 创建安全分析Agent
@function_tool
def get_device_status(device_id: str) -> str:
    """获取设备状态"""
    return f"设备 {device_id} 状态正常"

@function_tool
def analyze_alerts(alert_ids: list) -> str:
    """分析告警信息"""
    return f"分析了 {len(alert_ids)} 个告警"

security_agent = Agent(
    name="安全分析专家",
    instructions="你是网络安全专家，专注于威胁分析和安全建议",
    functions=[get_device_status, analyze_alerts],
    model=deepseek_client["model"]
)
        return ModelSettings(
            temperature=self.config.temperature,
            max_tokens=self.config.max_tokens,
            top_p=self.config.top_p,
            frequency_penalty=self.config.frequency_penalty,
            presence_penalty=self.config.presence_penalty,
            tool_choice="auto"
        )

# 网络监控工具函数实现
@function_tool
async def get_device_status(device_id: str) -> str:
    """获取设备状态信息

    Args:
        device_id: 设备ID，支持设备名称或IP地址

    Returns:
        设备状态的JSON字符串，包含基本信息、性能指标和连接状态
    """
    try:
        # 实际实现中查询数据库
        from database import get_db_session
        from models import Device, MonitoringData

        async with get_db_session() as session:
            # 查询设备基本信息
            device_query = select(Device).where(
                (Device.id == device_id) |
                (Device.ip_address == device_id) |
                (Device.name == device_id)
            )
            device = await session.execute(device_query)
            device = device.scalar_one_or_none()

            if not device:
                return json.dumps({
                    "error": f"设备 {device_id} 未找到",
                    "status": "not_found"
                })

            # 查询最新监控数据
            monitoring_query = select(MonitoringData).where(
                MonitoringData.device_id == device.id
            ).order_by(MonitoringData.timestamp.desc()).limit(10)

            monitoring_data = await session.execute(monitoring_query)
            latest_metrics = monitoring_data.scalars().all()

            # 构建响应数据
            status_data = {
                "device_id": device.id,
                "device_name": device.name,
                "ip_address": device.ip_address,
                "device_type": device.device_type,
                "status": device.status,
                "location": device.location,
                "last_seen": device.updated_at.isoformat(),
                "metrics": {}
            }

            # 处理监控指标
            for metric in latest_metrics:
                metric_key = f"{metric.metric_type}_{metric.metric_name}"
                status_data["metrics"][metric_key] = {
                    "value": metric.metric_value,
                    "unit": metric.unit,
                    "timestamp": metric.timestamp.isoformat()
                }

            return json.dumps(status_data, ensure_ascii=False)

    except Exception as e:
        logger.error(f"获取设备状态失败: {e}")
        return json.dumps({
            "error": f"获取设备 {device_id} 状态时发生错误: {str(e)}",
            "status": "error"
        })

@function_tool
async def query_alerts(
    severity: str = "high",
    time_range: str = "24h",
    device_type: Optional[str] = None,
    limit: int = 10
) -> str:
    """查询告警信息

    Args:
        severity: 告警级别 (critical, high, medium, low)
        time_range: 时间范围 (1h, 24h, 7d, 30d)
        device_type: 设备类型过滤 (switch, router, ap, server)
        limit: 返回结果数量限制

    Returns:
        告警信息的JSON字符串，包含告警列表和统计信息
    """
    try:
        from database import get_db_session
        from models import Alert, Device
        from datetime import datetime, timedelta

        # 解析时间范围
        time_delta_map = {
            "1h": timedelta(hours=1),
            "24h": timedelta(hours=24),
            "7d": timedelta(days=7),
            "30d": timedelta(days=30)
        }

        time_delta = time_delta_map.get(time_range, timedelta(hours=24))
        start_time = datetime.utcnow() - time_delta

        async with get_db_session() as session:
            # 构建查询
            query = select(Alert, Device).join(
                Device, Alert.device_id == Device.id
            ).where(
                Alert.created_at >= start_time
            )

            # 添加过滤条件
            if severity != "all":
                query = query.where(Alert.severity == severity)

            if device_type:
                query = query.where(Device.device_type == device_type)

            # 按时间倒序排列
            query = query.order_by(Alert.created_at.desc()).limit(limit)

            results = await session.execute(query)
            alert_data = results.all()

            # 构建响应数据
            alerts_list = []
            for alert, device in alert_data:
                alert_info = {
                    "alert_id": alert.id,
                    "title": alert.title,
                    "description": alert.description,
                    "severity": alert.severity,
                    "alert_type": alert.alert_type,
                    "status": alert.status,
                    "created_at": alert.created_at.isoformat(),
                    "device": {
                        "id": device.id,
                        "name": device.name,
                        "ip_address": device.ip_address,
                        "type": device.device_type,
                        "location": device.location
                    }
                }

                # 添加AI分析结果（如果有）
                if alert.deepseek_analysis:
                    alert_info["ai_analysis"] = alert.deepseek_analysis

                alerts_list.append(alert_info)

            # 统计信息
            stats_query = select(Alert.severity, func.count(Alert.id)).where(
                Alert.created_at >= start_time
            ).group_by(Alert.severity)

            if device_type:
                stats_query = stats_query.join(Device).where(
                    Device.device_type == device_type
                )

            stats_result = await session.execute(stats_query)
            severity_stats = dict(stats_result.all())

            response_data = {
                "alerts": alerts_list,
                "total_count": len(alerts_list),
                "query_params": {
                    "severity": severity,
                    "time_range": time_range,
                    "device_type": device_type,
                    "limit": limit
                },
                "statistics": {
                    "severity_distribution": severity_stats,
                    "query_time_range": {
                        "start": start_time.isoformat(),
                        "end": datetime.utcnow().isoformat()
                    }
                }
            }

            return json.dumps(response_data, ensure_ascii=False)

    except Exception as e:
        logger.error(f"查询告警信息失败: {e}")
        return json.dumps({
            "error": f"查询告警信息时发生错误: {str(e)}",
            "alerts": [],
            "total_count": 0
        })

@function_tool
async def analyze_network_performance(
    device_ids: List[str],
    metric_types: List[str] = ["cpu", "memory", "traffic"],
    time_range: str = "1h"
) -> str:
    """分析网络性能

    Args:
        device_ids: 设备ID列表
        metric_types: 指标类型列表
        time_range: 分析时间范围

    Returns:
        性能分析结果的JSON字符串
    """
    try:
        from database import get_db_session
        from models import MonitoringData, Device
        from datetime import datetime, timedelta
        import statistics

        time_delta_map = {
            "1h": timedelta(hours=1),
            "24h": timedelta(hours=24),
            "7d": timedelta(days=7)
        }

        time_delta = time_delta_map.get(time_range, timedelta(hours=1))
        start_time = datetime.utcnow() - time_delta

        async with get_db_session() as session:
            performance_data = {}

            for device_id in device_ids:
                # 查询设备信息
                device_query = select(Device).where(Device.id == device_id)
                device = await session.execute(device_query)
                device = device.scalar_one_or_none()

                if not device:
                    continue

                device_metrics = {}

                for metric_type in metric_types:
                    # 查询指标数据
                    metrics_query = select(MonitoringData).where(
                        MonitoringData.device_id == device_id,
                        MonitoringData.metric_type == metric_type,
                        MonitoringData.timestamp >= start_time
                    ).order_by(MonitoringData.timestamp)

                    metrics_result = await session.execute(metrics_query)
                    metrics = metrics_result.scalars().all()

                    if metrics:
                        values = [float(m.metric_value) for m in metrics if m.metric_value]

                        if values:
                            device_metrics[metric_type] = {
                                "current": values[-1],
                                "average": statistics.mean(values),
                                "max": max(values),
                                "min": min(values),
                                "trend": "increasing" if values[-1] > values[0] else "decreasing",
                                "data_points": len(values),
                                "unit": metrics[0].unit if metrics else None
                            }

                performance_data[device_id] = {
                    "device_name": device.name,
                    "device_type": device.device_type,
                    "metrics": device_metrics
                }

            return json.dumps(performance_data, ensure_ascii=False)

    except Exception as e:
        logger.error(f"分析网络性能失败: {e}")
        return json.dumps({
            "error": f"分析网络性能时发生错误: {str(e)}",
            "performance_data": {}
        })

@function_tool
async def get_threat_intelligence(ip_address: str) -> str:
    """查询威胁情报

    Args:
        ip_address: 要查询的IP地址

    Returns:
        威胁情报信息的JSON字符串
    """
    try:
        from database import get_db_session
        from models import ThreatIntelligence, IPBlacklist

        async with get_db_session() as session:
            # 查询威胁情报
            threat_query = select(ThreatIntelligence).where(
                ThreatIntelligence.ip_address == ip_address
            )
            threat_result = await session.execute(threat_query)
            threat_info = threat_result.scalar_one_or_none()

            # 查询黑名单
            blacklist_query = select(IPBlacklist).where(
                IPBlacklist.ip_address == ip_address
            )
            blacklist_result = await session.execute(blacklist_query)
            blacklist_info = blacklist_result.scalar_one_or_none()

            threat_data = {
                "ip_address": ip_address,
                "is_malicious": bool(threat_info or blacklist_info),
                "threat_level": "unknown",
                "categories": [],
                "last_seen": None,
                "sources": []
            }

            if threat_info:
                threat_data.update({
                    "threat_level": threat_info.threat_level,
                    "categories": threat_info.categories.split(",") if threat_info.categories else [],
                    "last_seen": threat_info.last_seen.isoformat() if threat_info.last_seen else None,
                    "sources": threat_info.sources.split(",") if threat_info.sources else [],
                    "description": threat_info.description
                })

            if blacklist_info:
                threat_data["blacklist_reason"] = blacklist_info.reason
                threat_data["blacklist_added"] = blacklist_info.created_at.isoformat()

            return json.dumps(threat_data, ensure_ascii=False)

    except Exception as e:
        logger.error(f"查询威胁情报失败: {e}")
        return json.dumps({
            "error": f"查询威胁情报时发生错误: {str(e)}",
            "ip_address": ip_address,
            "is_malicious": False
        })
```

#### 6.1.2 ant-design-x-vue对话组件实现
```vue
<template>
  <div class="ai-chat-container">
    <AXBubble
      v-for="message in messages"
      :key="message.id"
      :content="message.content"
      :type="message.type"
    />

    <AXPrompt
      v-model="inputValue"
      :loading="isLoading"
      @submit="handleSubmit"
    />
  </div>
</template>

<script setup lang="ts">
import { Bubble as AXBubble, Prompt as AXPrompt } from 'ant-design-x-vue'
import { ref } from 'vue'

const messages = ref([])
const inputValue = ref('')
const isLoading = ref(false)

const handleSubmit = async (value: string) => {
  if (!value.trim()) return

  isLoading.value = true

  // 添加用户消息
  messages.value.push({
    id: Date.now(),
    content: value,
    type: 'user'
  })

  try {
    // 调用AI API
    const response = await fetch('/api/v1/ai/chat', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ message: value })
    })

    const result = await response.json()

    // 添加AI回复
    messages.value.push({
      id: Date.now() + 1,
      content: result.response,
      type: 'ai'
    })

  } catch (error) {
    console.error('AI对话失败:', error)
  } finally {
    isLoading.value = false
    inputValue.value = ''
  }
}
}
</script>

<style scoped>
.ai-chat-container {
  height: 400px;
  display: flex;
  flex-direction: column;
}

</style>
```

#### 6.1.3 后端API实现
```python
from fastapi import APIRouter
from pydantic import BaseModel

class ChatRequest(BaseModel):
    message: str

class ChatResponse(BaseModel):
    response: str

router = APIRouter()

@router.post("/ai/chat", response_model=ChatResponse)
async def chat_with_ai(request: ChatRequest):
    # 调用DeepSeek V3 API
    response = await call_deepseek_api(request.message)
    return ChatResponse(response=response)

async def call_deepseek_api(message: str) -> str:
    # DeepSeek API调用实现
    return "这是AI的回复"
```

#### 6.1.3 Agent初始化和管理系统

**Agent工厂类实现**：
```python
class CampusGuardAgentFactory:
    """CampusGuard Agent工厂类"""

    def __init__(self, client_manager: DeepSeekClientManager, cost_control: CostControlConfig):
        self.client_manager = client_manager
        self.cost_control = cost_control
        self.agents = {}
        self.usage_tracker = UsageTracker()

    async def create_security_analyst_agent(self) -> Agent:
        """创建安全分析Agent"""
        agent = Agent(
            name="Security Analyst",
            instructions="""你是CampusGuard网络安全分析专家。你的职责包括：

1. **威胁识别与分析**：
   - 分析网络流量异常和安全事件
   - 识别潜在的网络攻击和入侵行为
   - 评估威胁的严重程度和影响范围

2. **设备状态监控**：
   - 监控网络设备的运行状态和性能指标
   - 识别设备异常和故障征象
   - 提供设备维护和优化建议

3. **告警分析处理**：
   - 分析告警的根本原因和关联性
   - 提供详细的威胁分析报告
   - 建议相应的处置措施和预防策略

4. **交互规范**：
   - 使用中文进行专业、准确的回答
   - 提供结构化的分析结果和建议
   - 引用具体的数据和指标支持分析结论
   - 在不确定时明确说明并建议进一步调查

使用提供的工具函数获取实时数据，基于数据进行专业分析。""",

            model=self.client_manager.get_litellm_model(),
            model_settings=self.client_manager.get_model_settings(),

            tools=[
                get_device_status,
                query_alerts,
                analyze_network_performance,
                get_threat_intelligence
            ],

            # 配置工具使用行为
            tool_use_behavior="auto",  # 自动决定是否使用工具
            reset_tool_choice=True,    # 工具调用后重置为auto
        )

        self.agents[AgentType.SECURITY_ANALYST] = agent
        return agent

    async def create_operations_assistant_agent(self) -> Agent:
        """创建运维助手Agent"""
        agent = Agent(
            name="Operations Assistant",
            instructions="""你是CampusGuard网络运维助手。你的职责包括：

1. **日常运维支持**：
   - 回答网络设备操作和配置问题
   - 提供故障排查和解决方案
   - 协助进行系统维护和优化

2. **数据查询服务**：
   - 根据用户需求查询设备状态和性能数据
   - 提供告警信息和历史趋势分析
   - 生成运维报告和统计信息

3. **智能建议**：
   - 基于监控数据提供优化建议
   - 预测潜在问题和维护需求
   - 推荐最佳实践和配置方案

4. **交互特点**：
   - 友好、耐心的对话方式
   - 提供步骤清晰的操作指导
   - 支持多轮对话和上下文理解
   - 主动询问澄清模糊的需求

你可以使用工具函数获取实时数据来回答用户问题。""",

            model=self.client_manager.get_litellm_model(),
            model_settings=ModelSettings(
                temperature=0.8,  # 稍高的温度使对话更自然
                max_tokens=2048,
                top_p=0.9
            ),

            tools=[
                get_device_status,
                query_alerts,
                analyze_network_performance,
                get_threat_intelligence
            ]
        )

        self.agents[AgentType.OPERATIONS_ASSISTANT] = agent
        return agent

    async def create_alert_explainer_agent(self) -> Agent:
        """创建告警解释Agent"""
        agent = Agent(
            name="Alert Explainer",
            instructions="""你是CampusGuard告警解释专家。你的职责包括：

1. **告警详细解释**：
   - 解释告警的技术原因和背景
   - 分析告警的触发条件和阈值
   - 说明告警的严重程度和紧急性

2. **影响评估**：
   - 评估告警对网络和业务的潜在影响
   - 分析可能的连锁反应和风险
   - 预测问题的发展趋势

3. **处置建议**：
   - 提供具体的处置步骤和操作指南
   - 建议预防措施和长期解决方案
   - 推荐相关的监控和检查项目

4. **关联分析**：
   - 识别相关的历史告警和模式
   - 分析告警之间的关联关系
   - 构建攻击链和事件时间线

使用专业术语但确保解释清晰易懂，提供可操作的建议。""",

            model=self.client_manager.get_litellm_model(),
            model_settings=ModelSettings(
                temperature=0.6,  # 中等温度保持专业性
                max_tokens=3072,
                top_p=0.85
            ),

            tools=[
                get_device_status,
                query_alerts,
                analyze_network_performance,
                get_threat_intelligence
            ]
        )

        self.agents[AgentType.ALERT_EXPLAINER] = agent
        return agent

    async def get_agent(self, agent_type: AgentType) -> Agent:
        """获取指定类型的Agent"""
        if agent_type not in self.agents:
            if agent_type == AgentType.SECURITY_ANALYST:
                await self.create_security_analyst_agent()
            elif agent_type == AgentType.OPERATIONS_ASSISTANT:
                await self.create_operations_assistant_agent()
            elif agent_type == AgentType.ALERT_EXPLAINER:
                await self.create_alert_explainer_agent()
            else:
                raise ValueError(f"不支持的Agent类型: {agent_type}")

        return self.agents[agent_type]

class UsageTracker:
    """使用量跟踪器"""

    def __init__(self):
        self.daily_usage = {}
        self.hourly_usage = {}
        self.request_cache = {}

    async def track_request(self, agent_type: str, tokens_used: int, cost: float):
        """跟踪请求使用量"""
        today = datetime.now().date().isoformat()
        current_hour = datetime.now().hour

        # 更新日使用量
        if today not in self.daily_usage:
            self.daily_usage[today] = {"tokens": 0, "cost": 0.0, "requests": 0}

        self.daily_usage[today]["tokens"] += tokens_used
        self.daily_usage[today]["cost"] += cost
        self.daily_usage[today]["requests"] += 1

        # 更新小时使用量
        hour_key = f"{today}_{current_hour}"
        if hour_key not in self.hourly_usage:
            self.hourly_usage[hour_key] = {"tokens": 0, "cost": 0.0, "requests": 0}

        self.hourly_usage[hour_key]["tokens"] += tokens_used
        self.hourly_usage[hour_key]["cost"] += cost
        self.hourly_usage[hour_key]["requests"] += 1

    def check_limits(self, config: CostControlConfig) -> Dict[str, bool]:
        """检查使用量限制"""
        today = datetime.now().date().isoformat()
        current_hour = datetime.now().hour
        hour_key = f"{today}_{current_hour}"

        daily_tokens = self.daily_usage.get(today, {}).get("tokens", 0)
        hourly_tokens = self.hourly_usage.get(hour_key, {}).get("tokens", 0)

        return {
            "daily_limit_ok": daily_tokens < config.daily_token_limit,
            "hourly_limit_ok": hourly_tokens < config.hourly_token_limit,
            "can_proceed": (
                daily_tokens < config.daily_token_limit and
                hourly_tokens < config.hourly_token_limit
            )
        }

class AgentErrorHandler:
    """Agent错误处理器"""

    @staticmethod
    async def handle_agent_error(error: Exception, agent_type: str, input_data: str) -> Dict[str, Any]:
        """处理Agent执行错误"""
        error_response = {
            "success": False,
            "error_type": type(error).__name__,
            "error_message": str(error),
            "agent_type": agent_type,
            "timestamp": datetime.utcnow().isoformat(),
            "retry_suggested": False,
            "fallback_response": None
        }

        if isinstance(error, MaxTurnsExceeded):
            error_response.update({
                "retry_suggested": True,
                "suggestion": "请简化您的问题或分步骤提问",
                "fallback_response": "抱歉，处理您的请求时达到了最大轮次限制。请尝试将复杂问题分解为更简单的部分。"
            })

        elif isinstance(error, ModelBehaviorError):
            error_response.update({
                "retry_suggested": True,
                "suggestion": "模型响应异常，建议重试",
                "fallback_response": "AI模型响应异常，请稍后重试。如果问题持续，请联系系统管理员。"
            })

        elif isinstance(error, AgentsException):
            error_response.update({
                "retry_suggested": False,
                "suggestion": "系统配置问题，请联系管理员",
                "fallback_response": "系统遇到配置问题，请联系管理员解决。"
            })

        elif "timeout" in str(error).lower():
            error_response.update({
                "retry_suggested": True,
                "suggestion": "请求超时，建议重试",
                "fallback_response": "请求处理超时，请稍后重试。"
            })

        elif "rate limit" in str(error).lower():
            error_response.update({
                "retry_suggested": True,
                "suggestion": "API调用频率过高，请稍后重试",
                "fallback_response": "当前请求过于频繁，请稍后重试。"
            })

        else:
            error_response.update({
                "retry_suggested": True,
                "suggestion": "未知错误，建议重试",
                "fallback_response": "处理请求时发生未知错误，请重试。如果问题持续，请联系技术支持。"
            })

        # 记录错误日志
        agents_logger.error(f"Agent错误 - 类型: {agent_type}, 错误: {error}, 输入: {input_data[:100]}...")

        return error_response

# 成本控制和限流策略
class CostController:
    """成本控制器"""

    def __init__(self, config: CostControlConfig):
        self.config = config
        self.cache = {}
        self.request_queue = asyncio.Queue()

    async def check_request_cost(self, estimated_tokens: int) -> bool:
        """检查请求成本"""
        estimated_cost = estimated_tokens * 0.000002  # DeepSeek定价估算
        return estimated_cost <= self.config.cost_threshold_per_request

    async def cache_response(self, cache_key: str, response: str, duration: int = None):
        """缓存响应"""
        if duration is None:
            duration = self.config.cache_duration

        self.cache[cache_key] = {
            "response": response,
            "timestamp": datetime.utcnow(),
            "expires_at": datetime.utcnow() + timedelta(seconds=duration)
        }

    async def get_cached_response(self, cache_key: str) -> Optional[str]:
        """获取缓存响应"""
        if cache_key in self.cache:
            cached_item = self.cache[cache_key]
            if datetime.utcnow() < cached_item["expires_at"]:
                return cached_item["response"]
            else:
                del self.cache[cache_key]
        return None

    def generate_cache_key(self, agent_type: str, input_text: str) -> str:
        """生成缓存键"""
        import hashlib
        content = f"{agent_type}:{input_text}"
        return hashlib.md5(content.encode()).hexdigest()
```

#### 6.1.4 Loguru日志系统配置最佳实践

**完整的loguru配置模块**：
```python
# config/logging_config.py
import os
import sys
from pathlib import Path
from loguru import logger

def setup_logging():
    """配置loguru日志系统"""

    # 创建日志目录
    log_dir = Path("./logs")
    log_dir.mkdir(exist_ok=True)

    # 移除默认处理器
    logger.remove()

    # 控制台输出（开发环境）
    if os.getenv("PYTHON_ENV") != "production":
        logger.add(
            sink=sys.stdout,
            level=os.getenv("LOG_LEVEL", "INFO"),
            format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | <level>{message}</level>",
            colorize=True,
            backtrace=True,
            diagnose=True
        )

    # 主日志文件
    logger.add(
        sink=log_dir / "campusguard.log",
        rotation=os.getenv("LOG_MAX_SIZE", "100 MB"),
        retention=os.getenv("LOGURU_RETENTION_DAYS", "10 days"),
        level=os.getenv("LOG_LEVEL_ROOT", "INFO"),
        format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}",
        encoding="utf-8",
        compression="gz",
        serialize=False,
        enqueue=True  # 异步写入
    )

    # 错误日志文件
    logger.add(
        sink=log_dir / "error.log",
        rotation="50 MB",
        retention="5 days",
        level="ERROR",
        format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message} | {exception}",
        encoding="utf-8",
        backtrace=True,
        diagnose=True,
        enqueue=True
    )

    # Agent专用日志
    logger.add(
        sink=log_dir / "agents.log",
        rotation="50 MB",
        retention="5 days",
        level=os.getenv("LOG_LEVEL_AGENTS", "INFO"),
        format="{time:YYYY-MM-DD HH:mm:ss} | {level} | AGENTS | {name}:{function}:{line} | {message}",
        encoding="utf-8",
        filter=lambda record: record["extra"].get("component") == "agents",
        enqueue=True
    )

    # 性能日志
    logger.add(
        sink=log_dir / "performance.log",
        rotation="100 MB",
        retention="3 days",
        level="DEBUG",
        format="{time:YYYY-MM-DD HH:mm:ss} | {level} | PERF | {name}:{function}:{line} | {message}",
        encoding="utf-8",
        filter=lambda record: record["extra"].get("component") == "performance",
        enqueue=True
    )

    # 安全日志
    logger.add(
        sink=log_dir / "security.log",
        rotation="200 MB",
        retention="30 days",
        level="INFO",
        format="{time:YYYY-MM-DD HH:mm:ss} | {level} | SECURITY | {name}:{function}:{line} | {message}",
        encoding="utf-8",
        filter=lambda record: record["extra"].get("component") == "security",
        enqueue=True
    )

# 创建专用logger实例
def get_logger(component: str = None):
    """获取专用logger实例"""
    if component:
        return logger.bind(component=component)
    return logger

# 预定义的logger实例
agents_logger = get_logger("agents")
performance_logger = get_logger("performance")
security_logger = get_logger("security")

# 性能监控装饰器
def log_performance(func):
    """性能监控装饰器"""
    import time
    import functools

    @functools.wraps(func)
    async def async_wrapper(*args, **kwargs):
        start_time = time.time()
        try:
            result = await func(*args, **kwargs)
            execution_time = time.time() - start_time
            performance_logger.info(f"{func.__name__} 执行时间: {execution_time:.3f}秒")
            return result
        except Exception as e:
            execution_time = time.time() - start_time
            performance_logger.error(f"{func.__name__} 执行失败，耗时: {execution_time:.3f}秒，错误: {e}")
            raise

    @functools.wraps(func)
    def sync_wrapper(*args, **kwargs):
        start_time = time.time()
        try:
            result = func(*args, **kwargs)
            execution_time = time.time() - start_time
            performance_logger.info(f"{func.__name__} 执行时间: {execution_time:.3f}秒")
            return result
        except Exception as e:
            execution_time = time.time() - start_time
            performance_logger.error(f"{func.__name__} 执行失败，耗时: {execution_time:.3f}秒，错误: {e}")
            raise

    if asyncio.iscoroutinefunction(func):
        return async_wrapper
    else:
        return sync_wrapper

# 安全事件记录装饰器
def log_security_event(event_type: str):
    """安全事件记录装饰器"""
    def decorator(func):
        import functools

        @functools.wraps(func)
        async def async_wrapper(*args, **kwargs):
            try:
                result = await func(*args, **kwargs)
                security_logger.info(f"安全事件: {event_type} - {func.__name__} 执行成功")
                return result
            except Exception as e:
                security_logger.warning(f"安全事件: {event_type} - {func.__name__} 执行失败: {e}")
                raise

        @functools.wraps(func)
        def sync_wrapper(*args, **kwargs):
            try:
                result = func(*args, **kwargs)
                security_logger.info(f"安全事件: {event_type} - {func.__name__} 执行成功")
                return result
            except Exception as e:
                security_logger.warning(f"安全事件: {event_type} - {func.__name__} 执行失败: {e}")
                raise

        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper

    return decorator
```

**在主应用中使用loguru**：
```python
# main.py
from config.logging_config import setup_logging, get_logger, agents_logger, performance_logger, security_logger

# 初始化日志系统
setup_logging()

# 获取主logger
logger = get_logger()

# 应用启动日志
logger.info("CampusGuard应用启动")

# 在不同模块中使用专用logger
@log_performance
async def process_network_data():
    """处理网络数据"""
    logger.info("开始处理网络数据")
    # 处理逻辑

@log_security_event("威胁检测")
async def detect_threats():
    """威胁检测"""
    security_logger.info("开始威胁检测")
    # 检测逻辑
```



## 7. 总结

### 7.1 技术架构总结
- **后端**：Python 3.13.2 + FastAPI + MySQL 8.0 + loguru
- **前端**：Vue.js 3.5.0 + Element Plus + ant-design-x-vue + G6
- **AI引擎**：OpenAI Agents + DeepSeek V3
- **开发工具**：venv + pip + Vite

### 7.2 核心功能实现
- ✅ 网络设备监控（SNMP + python3-nmap）
- ✅ 安全威胁检测（规则引擎）
- ✅ AI智能分析（DeepSeek V3）
- ✅ 可视化监控（G6网络拓扑图）
- ✅ AI对话界面（ant-design-x-vue）

### 7.3 简化优化成果
- **开发周期**：从16-20周缩短到10-12周
- **技术复杂度**：降低40%，专注核心功能
- **依赖库数量**：精简50%，只保留必需组件
- **代码示例**：简化为最佳实践，易于理解和实现

### 7.4 开发建议

#### 7.4.1 开发优先级
1. **第一阶段**：基础设备监控 + 数据库设计
2. **第二阶段**：告警系统 + 基础可视化
3. **第三阶段**：AI集成 + 对话界面
4. **第四阶段**：网络拓扑图 + 功能完善

#### 7.4.2 测试建议
- 建议编写单元测试验证核心功能
- 重点测试AI集成和数据采集模块

#### 7.4.3 关键实现要点
1. **环境配置**：
   - 创建.env文件配置DeepSeek API密钥
   - 使用venv创建虚拟环境
   - 安装requirements.txt中的依赖

2. **数据库初始化**：
   - 创建MySQL数据库：campusguard
   - 运行SQLAlchemy模型创建表结构
   - 配置数据库连接池

3. **OpenAI Agents集成**：
   - 配置DeepSeek V3自定义客户端
   - 实现Agent工具函数
   - 集成ant-design-x-vue对话组件

4. **SNMP监控实现**：
   - 配置pysnmp采集器
   - 实现设备发现和状态监控
   - 设置定时任务进行数据采集

#### 7.4.4 部署说明
- **开发环境**：Python 3.13.2 + Node.js 20.19+
- **生产环境**：建议使用Docker容器化部署
- **数据库**：MySQL 8.0，建议配置主从复制
- **前端构建**：使用Vite构建生产版本